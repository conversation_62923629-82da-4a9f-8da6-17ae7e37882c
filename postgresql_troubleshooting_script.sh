#!/bin/bash

# PostgreSQL Troubleshooting Script for server_002
# Based on Kamailio_PostgreSQL_Installation_Guide.md
# Execute this script on admin@ip-172-31-16-164

echo "=== PostgreSQL Troubleshooting Script ==="
echo "Server: server_002 (admin@ip-172-31-16-164)"
echo "Date: $(date)"
echo "=========================================="

# Function to print section headers
print_section() {
    echo ""
    echo "=== $1 ==="
    echo ""
}

# Function to run command and show output
run_cmd() {
    echo "Running: $1"
    echo "----------------------------------------"
    eval "$1"
    echo ""
}

print_section "1. DIAGNOSTIC PHASE - Current Status"

run_cmd "sudo systemctl status postgresql"

print_section "2. Check PostgreSQL Version and Packages"

run_cmd "sudo dpkg -l | grep postgresql"

print_section "3. Check All PostgreSQL Services"

run_cmd "sudo systemctl list-units | grep postgresql"
run_cmd "sudo systemctl status postgresql@*"

print_section "4. Check PostgreSQL Clusters"

run_cmd "sudo pg_lsclusters"

print_section "5. Check PostgreSQL Logs"

run_cmd "sudo journalctl -u postgresql --no-pager -n 20"
run_cmd "sudo journalctl -u postgresql@15-main --no-pager -n 20"

print_section "6. Check PostgreSQL Configuration"

run_cmd "sudo ls -la /etc/postgresql/"
run_cmd "sudo ls -la /etc/postgresql/15/main/ 2>/dev/null || echo 'PostgreSQL 15 config not found'"

print_section "7. Check Data Directory"

run_cmd "sudo ls -la /var/lib/postgresql/"
run_cmd "sudo ls -la /var/lib/postgresql/15/main/ 2>/dev/null || echo 'PostgreSQL 15 data directory not found'"

print_section "8. Check Running Processes"

run_cmd "ps aux | grep postgres | grep -v grep"

print_section "9. Check Listening Ports"

run_cmd "sudo ss -tlnp | grep 5432"

print_section "10. FIXING PHASE - Apply Fixes"

echo "Stopping generic postgresql service..."
run_cmd "sudo systemctl stop postgresql"

echo "Checking if postgresql@15-main exists..."
if systemctl list-units --full -all | grep -q "<EMAIL>"; then
    echo "Found postgresql@15-main service"
    run_cmd "sudo systemctl start postgresql@15-main"
    run_cmd "sudo systemctl enable postgresql@15-main"
else
    echo "postgresql@15-main not found, trying to start cluster manually..."
    run_cmd "sudo pg_ctlcluster 15 main start"
fi

print_section "11. VERIFICATION PHASE - Check Results"

run_cmd "sudo systemctl status postgresql@15-main"
run_cmd "sudo pg_lsclusters"
run_cmd "ps aux | grep postgres | grep -v grep"
run_cmd "sudo ss -tlnp | grep 5432"

print_section "12. Test Database Connectivity"

echo "Testing PostgreSQL connectivity..."
run_cmd "sudo -u postgres psql -c \"SELECT version();\""

echo "Checking Kamailio database..."
run_cmd "sudo -u postgres psql -c \"\\l\" | grep kamailio"

echo "Testing Kamailio database connection..."
run_cmd "PGPASSWORD=kamailio123 psql -h localhost -U kamailio -d kamailio -c \"SELECT * FROM version LIMIT 1;\" 2>/dev/null || echo 'Kamailio database connection failed - may need setup'"

print_section "13. Final Service Status Check"

run_cmd "sudo systemctl status postgresql@15-main"
run_cmd "sudo systemctl status kamailio"

print_section "14. Restart Kamailio Service"

echo "Restarting Kamailio service..."
run_cmd "sudo systemctl restart kamailio"
run_cmd "sudo systemctl status kamailio"

print_section "TROUBLESHOOTING COMPLETE"

echo "Summary of expected results:"
echo "✅ postgresql@15-main should show 'Active: active (running)'"
echo "✅ Multiple postgres processes should be visible"
echo "✅ Port 5432 should be listening"
echo "✅ Database connectivity should work"
echo "✅ Kamailio should start successfully"
echo ""
echo "If any issues remain, check the output above for error messages."
